<?php
class WxPay {
    private $appid;
    private $appsecret;
    private $mchid;
    private $key;
    private $notifyUrl;
    
    public function __construct() {
        $config = require __DIR__ . '/../config/wxpay.php';
        $this->appid = $config['appid'];
        $this->appsecret = $config['appsecret'];
        $this->mchid = $config['mchid'];
        $this->key = $config['key'];
        $this->notifyUrl = $config['notify_url'];
    }
    
    // 创建支付订单
    public function createOrder($orderNo, $amount, $description, $openid = null) {
        $url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';
        
        $params = [
            'appid' => $this->appid,
            'mch_id' => $this->mchid,
            'nonce_str' => $this->generateNonceStr(),
            'body' => $description,
            'out_trade_no' => $orderNo,
            'total_fee' => $amount * 100, // 转换为分
            'spbill_create_ip' => $this->getClientIp(),
            'notify_url' => $this->notifyUrl,
            'trade_type' => $openid ? 'JSAPI' : 'NATIVE'
        ];
        
        if ($openid) {
            $params['openid'] = $openid;
        }
        
        $params['sign'] = $this->generateSign($params);
        
        $xml = $this->arrayToXml($params);
        
        $response = $this->sendRequest($url, $xml);
        
        return $this->xmlToArray($response);
    }
    
    // 查询订单状态
    public function queryOrder($orderNo) {
        $url = 'https://api.mch.weixin.qq.com/pay/orderquery';
        
        $params = [
            'appid' => $this->appid,
            'mch_id' => $this->mchid,
            'out_trade_no' => $orderNo,
            'nonce_str' => $this->generateNonceStr()
        ];
        
        $params['sign'] = $this->generateSign($params);
        
        $xml = $this->arrayToXml($params);
        
        $response = $this->sendRequest($url, $xml);
        
        return $this->xmlToArray($response);
    }
    
    // 验证支付回调
    public function verifyNotify($xmlData) {
        $data = $this->xmlToArray($xmlData);
        
        if (!isset($data['sign'])) {
            return false;
        }
        
        $sign = $data['sign'];
        unset($data['sign']);
        
        $expectedSign = $this->generateSign($data);
        
        return $sign === $expectedSign;
    }
    
    // 生成签名
    private function generateSign($params) {
        ksort($params);
        
        $stringA = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $key !== 'sign') {
                $stringA .= $key . '=' . $value . '&';
            }
        }
        
        $stringSignTemp = $stringA . 'key=' . $this->key;
        
        return strtoupper(md5($stringSignTemp));
    }
    
    // 生成随机字符串
    private function generateNonceStr($length = 32) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
    
    // 获取客户端IP
    private function getClientIp() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    
    // 数组转XML
    private function arrayToXml($array) {
        $xml = '<xml>';
        foreach ($array as $key => $value) {
            $xml .= '<' . $key . '>' . $value . '</' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }
    
    // XML转数组
    private function xmlToArray($xml) {
        $array = [];
        $parser = xml_parser_create();
        xml_parse_into_struct($parser, $xml, $vals, $index);
        xml_parser_free($parser);
        
        foreach ($vals as $val) {
            if ($val['level'] == 2 && isset($val['value'])) {
                $array[$val['tag']] = $val['value'];
            }
        }
        
        return $array;
    }
    
    // 发送HTTP请求
    private function sendRequest($url, $data) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: text/xml'
        ]);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return $response;
    }
}
?>
