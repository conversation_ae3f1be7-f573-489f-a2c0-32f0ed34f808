<?php
session_start();
header('Content-Type: application/json');

// 检查是否已支付
if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
    echo json_encode(['success' => false, 'message' => '请先完成支付']);
    exit;
}

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/PhotoAPI.php';
require_once '../includes/QiniuUpload.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    if (!isset($_POST['image']) || empty($_POST['image'])) {
        throw new Exception('缺少图片数据');
    }
    
    $imageBase64 = $_POST['image'];
    $itemId = isset($_POST['item_id']) ? intval($_POST['item_id']) : 1;
    $colors = isset($_POST['colors']) ? $_POST['colors'] : 'blue';
    
    // 获取学生ID
    $studentId = $_SESSION['student_id'] ?? null;
    if (!$studentId) {
        throw new Exception('学生信息不存在');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 验证学生是否存在且已支付
    $student = $db->getStudentById($studentId);
    if (!$student) {
        throw new Exception('学生信息不存在');
    }
    
    if ($student['status'] !== 'paid') {
        throw new Exception('请先完成支付');
    }
    
    // 获取API密钥（从数据库配置或配置文件）
    $apiKey = 'YOUR_API_KEY'; // 需要替换为实际的API密钥
    
    // 初始化照片API
    $photoAPI = new PhotoAPI($apiKey);
    
    // 先上传原始图片到七牛云
    $qiniu = new QiniuUpload();
    $uploadResult = $qiniu->uploadBase64($imageBase64, 'jpg');
    
    if (isset($uploadResult['error'])) {
        throw new Exception('图片上传失败：' . $uploadResult['error']);
    }
    
    $originalImageUrl = $uploadResult['url'];
    
    // 调用API生成证件照（有水印版本，免费）
    $result = $photoAPI->makeIdPhoto($imageBase64, $itemId, $colors);
    
    if ($result['code'] !== 0) {
        throw new Exception('证件照生成失败：' . $result['msg']);
    }
    
    $picId = $result['data']['pic_id'];
    $photos = $result['data']['list'];
    
    // 保存照片记录到数据库
    $conn = $db->getConnection();
    
    // 检查是否已有照片记录
    $stmt = $conn->prepare("SELECT id FROM photo_records WHERE student_id = ?");
    $stmt->execute([$studentId]);
    $existingRecord = $stmt->fetch();
    
    if ($existingRecord) {
        // 更新现有记录
        $stmt = $conn->prepare("
            UPDATE photo_records 
            SET original_image_url = ?, pic_id = ?, generated_photos = ?, updated_at = NOW()
            WHERE student_id = ?
        ");
        $stmt->execute([
            $originalImageUrl,
            $picId,
            json_encode($photos),
            $studentId
        ]);
    } else {
        // 插入新记录
        $stmt = $conn->prepare("
            INSERT INTO photo_records (student_id, original_image_url, pic_id, generated_photos, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $studentId,
            $originalImageUrl,
            $picId,
            json_encode($photos)
        ]);
    }
    
    // 更新学生表的pic_id
    $stmt = $conn->prepare("UPDATE students SET pic_id = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$picId, $studentId]);
    
    // 返回成功结果
    echo json_encode([
        'success' => true,
        'pic_id' => $picId,
        'photos' => $photos,
        'original_url' => $originalImageUrl,
        'message' => '证件照生成成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
