<?php
// 主入口文件 - 信息采集表单页面
session_start();

// 引入配置文件
$dbConfig = require_once 'config/database.php';
$qiniuConfig = require_once 'config/qiniu.php';
$wxpayConfig = require_once 'config/wxpay.php';

// 引入工具类
require_once 'includes/Database.php';
require_once 'includes/QiniuUpload.php';
require_once 'includes/WxPay.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毕业信息采集系统</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <style>
        /* 添加一些动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .container {
            animation: fadeInUp 0.6s ease-out;
        }

        .form-group {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        .form-group:nth-child(5) { animation-delay: 0.5s; }

        .submit-btn {
            animation: fadeInUp 0.6s ease-out 0.6s both;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                    <path d="M19 15L20.09 18.26L23 19L20.09 19.74L19 23L17.91 19.74L15 19L17.91 18.26L19 15Z" fill="currentColor"/>
                    <path d="M5 15L6.09 18.26L9 19L6.09 19.74L5 23L3.91 19.74L1 19L3.91 18.26L5 15Z" fill="currentColor"/>
                </svg>
            </div>
            <h1>毕业信息采集系统</h1>
            <p>请填写以下信息，完成后需支付45元进行证件照制作</p>
            <div class="header-decoration">
                <div class="decoration-dot"></div>
                <div class="decoration-dot"></div>
                <div class="decoration-dot"></div>
            </div>
        </div>
        
        <form id="studentForm" class="form-container">
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="idcard">身份证号 *</label>
                <input type="text" id="idcard" name="idcard" required pattern="[0-9X]{18}">
            </div>
            
            <div class="form-group">
                <label for="phone">手机号 *</label>
                <input type="tel" id="phone" name="phone" required pattern="[0-9]{11}">
            </div>
            
            <div class="form-group">
                <label for="student_id">学号 *</label>
                <input type="text" id="student_id" name="student_id" required>
            </div>
            
            <div class="form-group">
                <label for="collection_code">采集码图片 *</label>
                <input type="file" id="collection_code" name="collection_code" accept="image/*" required>
                <div class="file-preview" id="codePreview"></div>
            </div>
            
            <button type="submit" class="submit-btn">提交信息并支付</button>
        </form>
    </div>
    
    <script src="assets/js/form.js"></script>
</body>
</html>
