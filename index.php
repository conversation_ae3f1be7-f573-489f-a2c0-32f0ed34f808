<?php
// 主入口文件 - 信息采集表单页面
session_start();

// 引入配置文件
$dbConfig = require_once 'config/database.php';
$qiniuConfig = require_once 'config/qiniu.php';
$wxpayConfig = require_once 'config/wxpay.php';

// 引入工具类
require_once 'includes/Database.php';
require_once 'includes/QiniuUpload.php';
require_once 'includes/WxPay.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毕业信息采集系统</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>毕业信息采集系统</h1>
            <p>请填写以下信息，完成后需支付45元进行证件照制作</p>
        </div>
        
        <form id="studentForm" class="form-container">
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="idcard">身份证号 *</label>
                <input type="text" id="idcard" name="idcard" required pattern="[0-9X]{18}">
            </div>
            
            <div class="form-group">
                <label for="phone">手机号 *</label>
                <input type="tel" id="phone" name="phone" required pattern="[0-9]{11}">
            </div>
            
            <div class="form-group">
                <label for="student_id">学号 *</label>
                <input type="text" id="student_id" name="student_id" required>
            </div>
            
            <div class="form-group">
                <label for="collection_code">采集码图片 *</label>
                <input type="file" id="collection_code" name="collection_code" accept="image/*" required>
                <div class="file-preview" id="codePreview"></div>
            </div>
            
            <button type="submit" class="submit-btn">提交信息并支付</button>
        </form>
    </div>
    
    <script src="assets/js/form.js"></script>
</body>
</html>
