/* 拍照页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 600;
}

.header p {
    font-size: 16px;
    opacity: 0.9;
}

.photo-section {
    padding: 40px;
}

/* 摄像头区域 */
.camera-container {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    border: 2px dashed #e1e5e9;
    border-radius: 12px;
    background: #f8f9fa;
}

#video {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#canvas {
    border-radius: 8px;
}

.camera-controls {
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    margin: 0 10px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(0);
}

/* 预览区域 */
.preview-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.original-photo,
.generated-photo {
    text-align: center;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    background: #f8f9fa;
}

.original-photo h3,
.generated-photo h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
}

.original-photo img,
.generated-photo img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 换装区域 */
.clothes-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    background: #f8f9fa;
}

.clothes-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    text-align: center;
}

.clothes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.clothes-item {
    text-align: center;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clothes-item:hover {
    border-color: #4facfe;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.2);
}

.clothes-item.selected {
    border-color: #4facfe;
    background: #f0f8ff;
}

.clothes-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 8px;
}

.clothes-item span {
    display: block;
    font-size: 12px;
    color: #666;
}

/* 操作按钮 */
.action-buttons {
    text-align: center;
    padding: 20px 0;
}

.action-buttons .btn {
    margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 8px;
    }
    
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .photo-section {
        padding: 20px;
    }
    
    .preview-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .camera-controls .btn {
        margin: 5px;
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .clothes-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .action-buttons .btn {
        margin: 5px;
        padding: 10px 16px;
        font-size: 14px;
    }
}
