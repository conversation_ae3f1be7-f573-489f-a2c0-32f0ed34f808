/* 拍照页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    padding: 1rem;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 600;
}

.header p {
    font-size: 1.125rem;
    opacity: 0.95;
    position: relative;
    z-index: 1;
    font-weight: 400;
    margin-bottom: 2rem;
}

.header-icon {
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

.header-icon svg {
    width: 3rem;
    height: 3rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.progress-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    transform: scale(1.1);
}

.step-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-weight: 500;
}

.progress-step.active .step-label {
    color: white;
    font-weight: 600;
}

.progress-line {
    width: 3rem;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 0.5rem;
    margin-bottom: 1.5rem;
}

.photo-section {
    padding: 2.5rem;
}

/* 摄像头区域 */
.camera-container {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.camera-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
    pointer-events: none;
}

#video {
    width: 100%;
    max-width: 480px;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 3px solid var(--bg-primary);
}

#canvas {
    border-radius: var(--radius-lg);
}

.camera-controls {
    margin-top: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.btn {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    min-width: 120px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--text-secondary) 0%, var(--text-primary) 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #0891b2 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

/* 预览区域 */
.preview-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.original-photo,
.generated-photo {
    text-align: center;
    padding: 2rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.original-photo:hover,
.generated-photo:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.original-photo::before,
.generated-photo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.03) 50%, transparent 70%);
    pointer-events: none;
}

.original-photo h3,
.generated-photo h3 {
    margin-bottom: 1.25rem;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.original-photo img,
.generated-photo img {
    max-width: 100%;
    max-height: 320px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 3px solid var(--bg-primary);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.original-photo img:hover,
.generated-photo img:hover {
    transform: scale(1.02);
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    position: relative;
}

.loading p {
    margin-bottom: 1.5rem;
    font-weight: 500;
    font-size: 1.125rem;
}

.loading::after {
    content: '';
    display: block;
    width: 3rem;
    height: 3rem;
    margin: 0 auto;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes modernSpin {
    0% {
        transform: rotate(0deg) scale(1);
        border-top-color: var(--primary-color);
    }
    25% {
        border-top-color: var(--secondary-color);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        border-top-color: var(--success-color);
    }
    75% {
        border-top-color: var(--warning-color);
    }
    100% {
        transform: rotate(360deg) scale(1);
        border-top-color: var(--primary-color);
    }
}

/* 现代化的脉冲加载动画 */
.loading-pulse {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    padding: 2rem;
}

.pulse-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background: var(--primary-color);
    animation: pulse-wave 1.4s ease-in-out infinite;
}

.pulse-dot:nth-child(2) {
    animation-delay: 0.2s;
    background: var(--secondary-color);
}

.pulse-dot:nth-child(3) {
    animation-delay: 0.4s;
    background: var(--success-color);
}

@keyframes pulse-wave {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 换装区域 */
.clothes-section {
    margin-bottom: 2rem;
    padding: 2rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.clothes-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.03) 50%, transparent 70%);
    pointer-events: none;
}

.clothes-section h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
    text-align: center;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.clothes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.clothes-item {
    text-align: center;
    padding: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.clothes-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
}

.clothes-item:hover::before {
    left: 100%;
}

.clothes-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.clothes-item.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.clothes-item img {
    width: 4rem;
    height: 4rem;
    object-fit: cover;
    border-radius: var(--radius-md);
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 1;
}

.clothes-item span {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.clothes-item.selected span {
    color: var(--primary-color);
    font-weight: 600;
}

/* 操作按钮 */
.action-buttons {
    text-align: center;
    padding: 20px 0;
}

.action-buttons .btn {
    margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 0.5rem;
    }

    .container {
        margin: 0;
        border-radius: var(--radius-lg);
    }

    .header {
        padding: 2rem 1.5rem;
    }

    .header h1 {
        font-size: 1.75rem;
    }

    .header p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .header-icon svg {
        width: 2.5rem;
        height: 2.5rem;
    }

    .progress-bar {
        max-width: 100%;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .progress-step {
        flex: 1;
        min-width: 60px;
    }

    .step-number {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .step-label {
        font-size: 0.7rem;
    }

    .progress-line {
        width: 2rem;
        margin: 0 0.25rem;
    }

    .photo-section {
        padding: 1.5rem;
    }

    .preview-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .camera-container {
        padding: 1.5rem;
    }

    .camera-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    .camera-controls .btn {
        width: 100%;
        max-width: 200px;
        padding: 0.75rem 1.25rem;
        font-size: 0.8rem;
    }

    .clothes-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 0.75rem;
    }

    .clothes-item {
        padding: 1rem;
    }

    .clothes-item img {
        width: 3rem;
        height: 3rem;
    }

    .clothes-item span {
        font-size: 0.8rem;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        align-items: center;
    }

    .action-buttons .btn {
        width: 100%;
        max-width: 200px;
        padding: 0.75rem 1.25rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 1.5rem 1rem;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .photo-section {
        padding: 1rem;
    }

    .camera-container {
        padding: 1rem;
    }

    .progress-bar {
        flex-direction: column;
        gap: 1rem;
    }

    .progress-line {
        width: 2px;
        height: 1rem;
        margin: 0;
    }

    .clothes-grid {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }

    .clothes-item img {
        width: 2.5rem;
        height: 2.5rem;
    }
}
