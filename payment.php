<?php
// 支付页面
session_start();

// 检查是否已提交表单
if (!isset($_SESSION['form_submitted']) || !$_SESSION['form_submitted']) {
    header('Location: index.php');
    exit;
}

// 获取学生ID
$studentId = $_GET['student_id'] ?? $_SESSION['student_id'] ?? null;
if (!$studentId) {
    header('Location: index.php');
    exit;
}

// 引入配置文件
$dbConfig = require_once 'config/database.php';
require_once 'includes/Database.php';
require_once 'includes/WxPay.php';

// 获取学生信息
$db = new Database();
$student = $db->getStudentById($studentId);

if (!$student) {
    header('Location: index.php');
    exit;
}

// 检查是否已支付
if ($student['status'] === 'paid' || $student['status'] === 'completed') {
    $_SESSION['payment_success'] = true;
    header('Location: photo.php');
    exit;
}

// 生成订单号
$orderNo = 'ORDER_' . date('YmdHis') . '_' . $studentId;
$amount = 45.00; // 支付金额
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付 - 毕业信息采集系统</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <style>
        .payment-container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .payment-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .payment-content {
            padding: 40px;
        }
        
        .student-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .student-info h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .payment-amount {
            text-align: center;
            margin: 30px 0;
        }
        
        .amount {
            font-size: 36px;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .payment-methods {
            margin: 30px 0;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: #4facfe;
        }
        
        .payment-method.selected {
            border-color: #4facfe;
            background: #f0f8ff;
        }
        
        .payment-method input[type="radio"] {
            margin-right: 15px;
        }
        
        .payment-method img {
            width: 40px;
            height: 40px;
            margin-right: 15px;
        }
        
        .qr-code-container {
            text-align: center;
            margin: 30px 0;
            display: none;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        
        .payment-status {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <h1>支付确认</h1>
            <p>请确认信息并完成支付</p>
        </div>
        
        <div class="payment-content">
            <div class="student-info">
                <h3>学生信息</h3>
                <div class="info-item">
                    <span>姓名：</span>
                    <span><?php echo htmlspecialchars($student['name']); ?></span>
                </div>
                <div class="info-item">
                    <span>身份证号：</span>
                    <span><?php echo htmlspecialchars(substr($student['idcard'], 0, 6) . '********' . substr($student['idcard'], -4)); ?></span>
                </div>
                <div class="info-item">
                    <span>手机号：</span>
                    <span><?php echo htmlspecialchars(substr($student['phone'], 0, 3) . '****' . substr($student['phone'], -4)); ?></span>
                </div>
                <div class="info-item">
                    <span>学号：</span>
                    <span><?php echo htmlspecialchars($student['student_id']); ?></span>
                </div>
            </div>
            
            <div class="payment-amount">
                <p>支付金额</p>
                <div class="amount">¥<?php echo number_format($amount, 2); ?></div>
                <p>证件照制作费用</p>
            </div>
            
            <div class="payment-methods">
                <div class="payment-method selected" data-method="wechat">
                    <input type="radio" name="payment_method" value="wechat" checked>
                    <span>微信支付</span>
                </div>
            </div>
            
            <button id="payBtn" class="submit-btn">立即支付</button>
            
            <div class="qr-code-container" id="qrCodeContainer">
                <p>请使用微信扫描二维码完成支付</p>
                <div class="qr-code" id="qrCode">
                    <p>生成二维码中...</p>
                </div>
                <p>订单号：<span id="orderNumber"><?php echo $orderNo; ?></span></p>
            </div>
            
            <div class="payment-status" id="paymentStatus">
                <p>正在检查支付状态...</p>
                <div class="loading"></div>
            </div>
        </div>
    </div>
    
    <script>
        $(document).ready(function() {
            let paymentTimer = null;
            
            // 支付方式选择
            $('.payment-method').on('click', function() {
                $('.payment-method').removeClass('selected');
                $(this).addClass('selected');
                $(this).find('input[type="radio"]').prop('checked', true);
            });
            
            // 支付按钮点击
            $('#payBtn').on('click', function() {
                const paymentMethod = $('input[name="payment_method"]:checked').val();
                const studentId = <?php echo $studentId; ?>;
                const orderNo = '<?php echo $orderNo; ?>';
                const amount = <?php echo $amount; ?>;
                
                $(this).prop('disabled', true).text('创建订单中...');
                
                // 创建支付订单
                $.ajax({
                    url: 'api/create_payment.php',
                    type: 'POST',
                    data: {
                        student_id: studentId,
                        order_no: orderNo,
                        amount: amount,
                        payment_method: paymentMethod
                    },
                    success: function(response) {
                        if (response.success) {
                            // 显示二维码
                            $('#qrCodeContainer').show();
                            $('#paymentStatus').show();
                            
                            if (response.qr_code) {
                                $('#qrCode').html('<img src="' + response.qr_code + '" alt="支付二维码">');
                            }
                            
                            // 开始检查支付状态
                            checkPaymentStatus(orderNo);
                        } else {
                            alert('创建订单失败：' + response.message);
                            $('#payBtn').prop('disabled', false).text('立即支付');
                        }
                    },
                    error: function() {
                        alert('创建订单失败，请重试');
                        $('#payBtn').prop('disabled', false).text('立即支付');
                    }
                });
            });
            
            // 检查支付状态
            function checkPaymentStatus(orderNo) {
                paymentTimer = setInterval(function() {
                    $.ajax({
                        url: 'api/check_payment.php',
                        type: 'GET',
                        data: { order_no: orderNo },
                        success: function(response) {
                            if (response.success) {
                                if (response.status === 'success') {
                                    clearInterval(paymentTimer);
                                    $('#paymentStatus').html('<p style="color: green;">支付成功！正在跳转...</p>');
                                    
                                    setTimeout(function() {
                                        window.location.href = 'photo.php';
                                    }, 2000);
                                } else if (response.status === 'failed') {
                                    clearInterval(paymentTimer);
                                    $('#paymentStatus').html('<p style="color: red;">支付失败，请重试</p>');
                                    $('#payBtn').prop('disabled', false).text('立即支付');
                                }
                            }
                        }
                    });
                }, 3000); // 每3秒检查一次
            }
            
            // 页面卸载时清除定时器
            $(window).on('beforeunload', function() {
                if (paymentTimer) {
                    clearInterval(paymentTimer);
                }
            });
        });
    </script>
</body>
</html>
