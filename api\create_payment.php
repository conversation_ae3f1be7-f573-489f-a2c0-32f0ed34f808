<?php
session_start();
header('Content-Type: application/json');

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/WxPay.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    $requiredFields = ['student_id', 'order_no', 'amount', 'payment_method'];
    foreach ($requiredFields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception('缺少必要字段：' . $field);
        }
    }
    
    $studentId = intval($_POST['student_id']);
    $orderNo = $_POST['order_no'];
    $amount = floatval($_POST['amount']);
    $paymentMethod = $_POST['payment_method'];
    
    // 验证金额
    if ($amount <= 0) {
        throw new Exception('支付金额无效');
    }
    
    // 验证支付方式
    if (!in_array($paymentMethod, ['wechat', 'alipay'])) {
        throw new Exception('不支持的支付方式');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 验证学生是否存在
    $student = $db->getStudentById($studentId);
    if (!$student) {
        throw new Exception('学生信息不存在');
    }
    
    // 检查是否已支付
    if ($student['status'] === 'paid' || $student['status'] === 'completed') {
        throw new Exception('该订单已支付');
    }
    
    // 检查订单号是否已存在
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT id FROM payments WHERE order_no = ?");
    $stmt->execute([$orderNo]);
    if ($stmt->fetch()) {
        throw new Exception('订单号已存在');
    }
    
    // 插入支付记录
    $stmt = $conn->prepare("
        INSERT INTO payments (student_id, order_no, amount, payment_method, status, created_at)
        VALUES (?, ?, ?, ?, 'pending', NOW())
    ");
    $stmt->execute([$studentId, $orderNo, $amount, $paymentMethod]);
    $paymentId = $conn->lastInsertId();
    
    // 保存支付ID到session
    $_SESSION['payment_id'] = $paymentId;
    
    $qrCode = null;
    $paymentUrl = null;
    
    if ($paymentMethod === 'wechat') {
        // 创建微信支付订单
        $wxPay = new WxPay();
        $description = '毕业信息采集-证件照制作';
        
        $result = $wxPay->createOrder($orderNo, $amount, $description);
        
        if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
            $qrCode = $result['code_url']; // 二维码链接
            
            // 更新支付记录
            $stmt = $conn->prepare("
                UPDATE payments 
                SET transaction_id = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$result['prepay_id'] ?? '', $paymentId]);
        } else {
            throw new Exception('创建微信支付订单失败：' . ($result['err_code_des'] ?? $result['return_msg']));
        }
    }
    
    // 返回成功结果
    echo json_encode([
        'success' => true,
        'payment_id' => $paymentId,
        'order_no' => $orderNo,
        'qr_code' => $qrCode,
        'payment_url' => $paymentUrl,
        'amount' => $amount,
        'message' => '支付订单创建成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
