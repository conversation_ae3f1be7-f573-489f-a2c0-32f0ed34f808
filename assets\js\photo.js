$(document).ready(function() {
    let stream = null;
    let currentPicId = null;
    let selected<PERSON>lothe = null;

    // 更新进度条
    function updateProgressStep(step) {
        $('.progress-step').removeClass('active');
        for (let i = 1; i <= step; i++) {
            $(`.progress-step:nth-child(${i * 2 - 1})`).addClass('active');
        }
    }
    
    // 开启摄像头
    $('#startCamera').on('click', function() {
        navigator.mediaDevices.getUserMedia({ 
            video: { 
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            } 
        })
        .then(function(mediaStream) {
            stream = mediaStream;
            const video = document.getElementById('video');
            video.srcObject = stream;
            video.play();
            
            $('#startCamera').hide();
            $('#takePhoto').show();
        })
        .catch(function(err) {
            console.error('无法访问摄像头:', err);
            alert('无法访问摄像头，请检查权限设置或使用上传功能');
        });
    });
    
    // 拍照
    $('#takePhoto').on('click', function() {
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        ctx.drawImage(video, 0, 0);
        
        // 显示拍摄的照片
        const imageData = canvas.toDataURL('image/jpeg', 0.8);
        $('#originalImg').attr('src', imageData).show();
        
        // 停止摄像头
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        $('#video').hide();
        $('#takePhoto').hide();
        $('#startCamera').show();
        
        // 生成证件照
        generateIdPhoto(imageData);
    });
    
    // 上传照片
    $('#uploadBtn').on('click', function() {
        $('#uploadPhoto').click();
    });
    
    $('#uploadPhoto').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 验证文件类型
            if (!file.type.match(/^image\/(jpeg|jpg|png|gif)$/)) {
                alert('请选择有效的图片文件（JPG、PNG、GIF）');
                return;
            }
            
            // 验证文件大小（8MB）
            if (file.size > 8 * 1024 * 1024) {
                alert('图片文件不能超过8MB');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;
                $('#originalImg').attr('src', imageData).show();
                
                // 生成证件照
                generateIdPhoto(imageData);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 生成证件照
    function generateIdPhoto(imageData) {
        $('#loading').show();
        $('#loadingPulse').show();
        $('#generatedImg').hide();
        $('#actionButtons').hide();
        $('#clothesSection').hide();

        // 更新进度条
        updateProgressStep(2);
        
        // 移除base64前缀
        const base64Data = imageData.split(',')[1];
        
        $.ajax({
            url: 'api/generate_photo.php',
            type: 'POST',
            data: {
                image: base64Data,
                item_id: 1, // 二寸照片
                colors: 'blue' // 蓝底
            },
            success: function(response) {
                $('#loading').hide();
                $('#loadingPulse').hide();

                if (response.success) {
                    currentPicId = response.pic_id;

                    // 显示生成的照片
                    if (response.photos && response.photos.blue) {
                        $('#generatedImg').attr('src', response.photos.blue).show();
                        $('#actionButtons').show();

                        // 更新进度条
                        updateProgressStep(3);

                        // 加载服装选项
                        loadClothesOptions();
                    } else {
                        alert('照片生成失败，请重试');
                        updateProgressStep(1);
                    }
                } else {
                    alert('照片生成失败：' + response.message);
                    updateProgressStep(1);
                }
            },
            error: function(xhr, status, error) {
                $('#loading').hide();
                $('#loadingPulse').hide();
                console.error('生成照片错误:', error);
                alert('照片生成失败，请重试');
                updateProgressStep(1);
            }
        });
    }
    
    // 加载服装选项
    function loadClothesOptions() {
        $.ajax({
            url: 'api/get_clothes.php',
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    const clothesGrid = $('#clothesGrid');
                    clothesGrid.empty();
                    
                    $.each(response.clothes, function(key, name) {
                        const clothesItem = $(`
                            <div class="clothes-item" data-clothe="${key}">
                                <div class="clothes-preview"></div>
                                <span>${name}</span>
                            </div>
                        `);
                        clothesGrid.append(clothesItem);
                    });
                    
                    // 绑定点击事件
                    $('.clothes-item').on('click', function() {
                        const clothe = $(this).data('clothe');
                        changeClothes(clothe);
                        
                        $('.clothes-item').removeClass('selected');
                        $(this).addClass('selected');
                    });
                }
            }
        });
    }
    
    // 更换服装
    function changeClothes(clothe) {
        if (!currentPicId) {
            alert('请先生成证件照');
            return;
        }
        
        $('#loading').show();
        $('#generatedImg').hide();
        
        $.ajax({
            url: 'api/change_clothes.php',
            type: 'POST',
            data: {
                pic_id: currentPicId,
                clothe: clothe
            },
            success: function(response) {
                $('#loading').hide();
                
                if (response.success) {
                    selectedClothe = clothe;
                    
                    // 显示换装后的照片
                    if (response.photos && response.photos.blue) {
                        $('#generatedImg').attr('src', response.photos.blue).show();
                    } else {
                        alert('换装失败，请重试');
                    }
                } else {
                    alert('换装失败：' + response.message);
                    $('#generatedImg').show();
                }
            },
            error: function(xhr, status, error) {
                $('#loading').hide();
                $('#generatedImg').show();
                console.error('换装错误:', error);
                alert('换装失败，请重试');
            }
        });
    }
    
    // 重新拍照
    $('#retakeBtn').on('click', function() {
        $('#originalImg').hide();
        $('#generatedImg').hide();
        $('#actionButtons').hide();
        $('#clothesSection').hide();
        currentPicId = null;
        selectedClothe = null;
        
        $('.clothes-item').removeClass('selected');
    });
    
    // 更换服装按钮
    $('#changeClothesBtn').on('click', function() {
        if ($('#clothesSection').is(':visible')) {
            $('#clothesSection').hide();
        } else {
            $('#clothesSection').show();
        }
    });
    
    // 确认并保存
    $('#confirmBtn').on('click', function() {
        if (!currentPicId) {
            alert('请先生成证件照');
            return;
        }

        const confirmBtn = $('#confirmBtn');
        confirmBtn.prop('disabled', true).text('保存中...');

        // 更新进度条到最后一步
        updateProgressStep(4);

        $.ajax({
            url: 'api/save_photo.php',
            type: 'POST',
            data: {
                pic_id: currentPicId,
                clothe: selectedClothe
            },
            success: function(response) {
                if (response.success) {
                    alert('照片保存成功！');
                    // 跳转到完成页面或返回首页
                    window.location.href = 'success.php';
                } else {
                    alert('保存失败：' + response.message);
                    confirmBtn.prop('disabled', false).text('确认并保存');
                    updateProgressStep(3);
                }
            },
            error: function(xhr, status, error) {
                console.error('保存错误:', error);
                alert('保存失败，请重试');
                confirmBtn.prop('disabled', false).text('确认并保存');
                updateProgressStep(3);
            }
        });
    });
    
    // 页面加载时检查是否有未完成的照片处理
    checkExistingPhoto();
    
    function checkExistingPhoto() {
        const studentId = sessionStorage.getItem('student_id');
        if (studentId) {
            $.ajax({
                url: 'api/check_photo_status.php',
                type: 'GET',
                data: { student_id: studentId },
                success: function(response) {
                    if (response.success && response.has_photo) {
                        // 如果已有照片，显示相关信息
                        if (response.photo_url) {
                            $('#generatedImg').attr('src', response.photo_url).show();
                            $('#actionButtons').show();
                        }
                        
                        if (response.pic_id) {
                            currentPicId = response.pic_id;
                            loadClothesOptions();
                        }
                    }
                }
            });
        }
    }
});
