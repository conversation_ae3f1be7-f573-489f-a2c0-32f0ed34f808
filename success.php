<?php
// 完成页面
session_start();

// 检查是否已完成照片处理
if (!isset($_SESSION['photo_completed']) || !$_SESSION['photo_completed']) {
    header('Location: index.php');
    exit;
}

// 获取学生ID
$studentId = $_SESSION['student_id'] ?? null;
if (!$studentId) {
    header('Location: index.php');
    exit;
}

// 引入配置文件
require_once 'includes/Database.php';

// 获取学生完整信息
$db = new Database();
$conn = $db->getConnection();

$stmt = $conn->prepare("
    SELECT s.*, p.order_no, p.amount, p.paid_at, pr.final_photo_url
    FROM students s
    LEFT JOIN payments p ON s.id = p.student_id AND p.status = 'success'
    LEFT JOIN photo_records pr ON s.id = pr.student_id
    WHERE s.id = ?
");
$stmt->execute([$studentId]);
$studentInfo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$studentInfo) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完成 - 毕业信息采集系统</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .success-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .success-header {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .success-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }
        
        .success-content {
            padding: 40px;
        }
        
        .student-summary {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .summary-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .photo-preview {
            text-align: center;
            margin: 30px 0;
        }
        
        .photo-preview img {
            max-width: 200px;
            max-height: 250px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .completion-time {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-header">
            <div class="success-icon">✅</div>
            <h1>信息采集完成</h1>
            <p>您的毕业信息已成功采集并生成证件照</p>
        </div>
        
        <div class="success-content">
            <div class="student-summary">
                <h3>采集信息汇总</h3>
                <div class="summary-item">
                    <span>姓名：</span>
                    <span><?php echo htmlspecialchars($studentInfo['name']); ?></span>
                </div>
                <div class="summary-item">
                    <span>学号：</span>
                    <span><?php echo htmlspecialchars($studentInfo['student_id']); ?></span>
                </div>
                <div class="summary-item">
                    <span>手机号：</span>
                    <span><?php echo htmlspecialchars($studentInfo['phone']); ?></span>
                </div>
                <div class="summary-item">
                    <span>订单号：</span>
                    <span><?php echo htmlspecialchars($studentInfo['order_no'] ?? '无'); ?></span>
                </div>
                <div class="summary-item">
                    <span>支付金额：</span>
                    <span>¥<?php echo number_format($studentInfo['amount'] ?? 0, 2); ?></span>
                </div>
                <div class="summary-item">
                    <span>支付时间：</span>
                    <span><?php echo $studentInfo['paid_at'] ? date('Y-m-d H:i:s', strtotime($studentInfo['paid_at'])) : '无'; ?></span>
                </div>
                <div class="summary-item">
                    <span>状态：</span>
                    <span style="color: green; font-weight: bold;">已完成</span>
                </div>
            </div>
            
            <?php if ($studentInfo['final_photo_url']): ?>
            <div class="photo-preview">
                <h3>您的证件照</h3>
                <img src="<?php echo htmlspecialchars($studentInfo['final_photo_url']); ?>" alt="证件照">
                <p>二寸蓝底证件照</p>
            </div>
            <?php endif; ?>
            
            <div class="action-buttons">
                <?php if ($studentInfo['final_photo_url']): ?>
                <a href="<?php echo htmlspecialchars($studentInfo['final_photo_url']); ?>" 
                   class="btn btn-primary" target="_blank" download>下载证件照</a>
                <?php endif; ?>
                <a href="index.php" class="btn btn-success">返回首页</a>
            </div>
            
            <div class="completion-time">
                完成时间：<?php echo date('Y年m月d日 H:i:s'); ?>
            </div>
        </div>
    </div>
    
    <script>
        // 清除session存储
        sessionStorage.clear();
        
        // 5秒后自动清除PHP session中的完成标记
        setTimeout(function() {
            fetch('api/clear_session.php', { method: 'POST' });
        }, 5000);
    </script>
</body>
</html>
