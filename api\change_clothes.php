<?php
session_start();
header('Content-Type: application/json');

// 调试模式：跳过支付检查
$debug_mode = true;

if (!$debug_mode) {
    // 检查是否已支付
    if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
        echo json_encode(['success' => false, 'message' => '请先完成支付']);
        exit;
    }
}

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/PhotoAPI.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    if (!isset($_POST['pic_id']) || empty($_POST['pic_id'])) {
        throw new Exception('缺少pic_id参数');
    }
    
    if (!isset($_POST['clothe']) || empty($_POST['clothe'])) {
        throw new Exception('缺少服装参数');
    }
    
    $picId = $_POST['pic_id'];
    $clothe = $_POST['clothe'];
    
    // 获取学生ID
    $studentId = $_SESSION['student_id'] ?? null;
    if (!$studentId) {
        throw new Exception('学生信息不存在');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 验证学生是否存在且已支付
    $student = $db->getStudentById($studentId);
    if (!$student) {
        throw new Exception('学生信息不存在');
    }
    
    if ($student['status'] !== 'paid') {
        throw new Exception('请先完成支付');
    }
    
    // 验证pic_id是否属于当前学生
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT id FROM photo_records WHERE student_id = ? AND pic_id = ?");
    $stmt->execute([$studentId, $picId]);
    $photoRecord = $stmt->fetch();
    
    if (!$photoRecord) {
        throw new Exception('无效的pic_id');
    }
    
    // 获取API密钥
    $apiConfig = require_once '../config/api.php';
    $apiKey = $apiConfig['photo_api_key'];
    
    // 初始化照片API
    $photoAPI = new PhotoAPI($apiKey);
    
    // 调用API更换服装
    $result = $photoAPI->changeClothes($picId, $clothe);
    
    if ($result['code'] !== 0) {
        throw new Exception('换装失败：' . $result['msg']);
    }
    
    $photos = $result['data']['list'];
    
    // 更新数据库中的服装选择
    $stmt = $conn->prepare("
        UPDATE photo_records 
        SET selected_clothe = ?, updated_at = NOW()
        WHERE student_id = ? AND pic_id = ?
    ");
    $stmt->execute([$clothe, $studentId, $picId]);
    
    // 同时更新学生表
    $stmt = $conn->prepare("UPDATE students SET clothe = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$clothe, $studentId]);
    
    // 返回成功结果
    echo json_encode([
        'success' => true,
        'photos' => $photos,
        'clothe' => $clothe,
        'message' => '换装成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
