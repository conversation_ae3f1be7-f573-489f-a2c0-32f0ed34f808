-- 毕业信息采集系统数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS biye_caiji CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE biye_caiji;

-- 学生信息表
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    idcard VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
    phone VARCHAR(11) NOT NULL COMMENT '手机号',
    student_id VARCHAR(50) NOT NULL COMMENT '学号',
    collection_code_url VARCHAR(500) NULL COMMENT '采集码图片URL',
    photo_url VARCHAR(500) NULL COMMENT '证件照URL',
    pic_id VARCHAR(50) NULL COMMENT 'API返回的pic_id',
    clothe VARCHAR(50) NULL COMMENT '选择的服装',
    status ENUM('pending', 'paid', 'completed') DEFAULT 'pending' COMMENT '状态：待支付、已支付、已完成',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_idcard (idcard),
    INDEX idx_phone (phone),
    INDEX idx_student_id (student_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生信息表';

-- 支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL COMMENT '学生ID',
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    payment_method ENUM('wechat', 'alipay') DEFAULT 'wechat' COMMENT '支付方式',
    status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '支付状态',
    transaction_id VARCHAR(100) NULL COMMENT '第三方交易号',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_id (student_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- 照片处理记录表
CREATE TABLE IF NOT EXISTS photo_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL COMMENT '学生ID',
    original_image_url VARCHAR(500) NULL COMMENT '原始照片URL',
    pic_id VARCHAR(50) NULL COMMENT 'API返回的pic_id',
    generated_photos JSON NULL COMMENT '生成的照片信息（JSON格式）',
    selected_clothe VARCHAR(50) NULL COMMENT '选择的服装',
    final_photo_url VARCHAR(500) NULL COMMENT '最终照片URL',
    api_cost DECIMAL(8,2) DEFAULT 0.00 COMMENT 'API调用费用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    INDEX idx_student_id (student_id),
    INDEX idx_pic_id (pic_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='照片处理记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NULL COMMENT '配置值',
    description VARCHAR(255) NULL COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('photo_api_key', '', '证件照API密钥'),
('payment_amount', '45.00', '支付金额'),
('photo_item_id', '1', '证件照规格ID（二寸）'),
('photo_colors', 'blue', '证件照背景色'),
('max_file_size', '8388608', '最大文件上传大小（8MB）'),
('allowed_file_types', 'jpg,jpeg,png,gif', '允许的文件类型'),
('site_title', '毕业信息采集系统', '网站标题'),
('site_description', '毕业生信息采集和证件照制作系统', '网站描述');

-- 创建视图：学生完整信息
CREATE VIEW student_full_info AS
SELECT 
    s.id,
    s.name,
    s.idcard,
    s.phone,
    s.student_id,
    s.collection_code_url,
    s.photo_url,
    s.pic_id,
    s.clothe,
    s.status,
    s.created_at,
    s.updated_at,
    p.order_no,
    p.amount,
    p.payment_method,
    p.status as payment_status,
    p.transaction_id,
    p.paid_at,
    pr.original_image_url,
    pr.generated_photos,
    pr.final_photo_url,
    pr.api_cost
FROM students s
LEFT JOIN payments p ON s.id = p.student_id AND p.status = 'success'
LEFT JOIN photo_records pr ON s.id = pr.student_id
ORDER BY s.created_at DESC;

-- 创建存储过程：获取统计信息
DELIMITER //
CREATE PROCEDURE GetStatistics()
BEGIN
    SELECT 
        COUNT(*) as total_students,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_students,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_students,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_students,
        COALESCE(SUM(CASE WHEN p.status = 'success' THEN p.amount END), 0) as total_revenue,
        COUNT(CASE WHEN p.status = 'success' THEN 1 END) as successful_payments
    FROM students s
    LEFT JOIN payments p ON s.id = p.student_id;
END //
DELIMITER ;

-- 创建触发器：更新学生状态
DELIMITER //
CREATE TRIGGER update_student_status_after_payment
AFTER UPDATE ON payments
FOR EACH ROW
BEGIN
    IF NEW.status = 'success' AND OLD.status != 'success' THEN
        UPDATE students SET status = 'paid' WHERE id = NEW.student_id;
    END IF;
END //
DELIMITER ;

-- 创建触发器：完成照片处理后更新状态
DELIMITER //
CREATE TRIGGER update_student_status_after_photo
AFTER UPDATE ON photo_records
FOR EACH ROW
BEGIN
    IF NEW.final_photo_url IS NOT NULL AND OLD.final_photo_url IS NULL THEN
        UPDATE students SET status = 'completed' WHERE id = NEW.student_id;
    END IF;
END //
DELIMITER ;
