<?php
session_start();
header('Content-Type: application/json');

// 引入必要的类
require_once '../includes/Database.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('请求方法不正确');
    }
    
    // 获取学生ID
    $studentId = $_GET['student_id'] ?? $_SESSION['student_id'] ?? null;
    if (!$studentId) {
        throw new Exception('学生信息不存在');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 获取学生信息
    $student = $db->getStudentById($studentId);
    if (!$student) {
        throw new Exception('学生信息不存在');
    }
    
    // 获取照片记录
    $conn = $db->getConnection();
    $stmt = $conn->prepare("
        SELECT pic_id, original_image_url, generated_photos, selected_clothe, final_photo_url
        FROM photo_records 
        WHERE student_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$studentId]);
    $photoRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $hasPhoto = false;
    $photoUrl = null;
    $picId = null;
    $selectedClothe = null;
    $generatedPhotos = null;
    
    if ($photoRecord) {
        $hasPhoto = true;
        $picId = $photoRecord['pic_id'];
        $selectedClothe = $photoRecord['selected_clothe'];
        
        // 如果有最终照片，使用最终照片
        if ($photoRecord['final_photo_url']) {
            $photoUrl = $photoRecord['final_photo_url'];
        } else if ($photoRecord['generated_photos']) {
            // 否则使用生成的照片
            $generatedPhotos = json_decode($photoRecord['generated_photos'], true);
            $photoUrl = $generatedPhotos['blue'] ?? null;
        }
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'has_photo' => $hasPhoto,
        'photo_url' => $photoUrl,
        'pic_id' => $picId,
        'selected_clothe' => $selectedClothe,
        'generated_photos' => $generatedPhotos,
        'student_status' => $student['status'],
        'message' => '查询成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
