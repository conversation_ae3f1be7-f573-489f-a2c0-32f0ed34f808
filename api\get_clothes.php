<?php
session_start();
header('Content-Type: application/json');

// 调试模式：跳过支付检查
$debug_mode = true;

if (!$debug_mode) {
    // 检查是否已支付
    if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
        echo json_encode(['success' => false, 'message' => '请先完成支付']);
        exit;
    }
}

// 引入必要的类
require_once '../includes/PhotoAPI.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('请求方法不正确');
    }
    
    // 获取API密钥
    $apiConfig = require_once '../config/api.php';
    $apiKey = $apiConfig['photo_api_key'];
    
    // 初始化照片API
    $photoAPI = new PhotoAPI($apiKey);
    
    // 获取服装选项
    $clothes = $photoAPI->getClothesOptions();
    
    // 返回服装列表
    echo json_encode([
        'success' => true,
        'clothes' => $clothes,
        'message' => '获取服装列表成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
