/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    padding: 1rem;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 640px;
    margin: 0 auto;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
    letter-spacing: -0.025em;
}

.header p {
    font-size: 1.125rem;
    opacity: 0.95;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.header-icon {
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

.header-icon svg {
    width: 3rem;
    height: 3rem;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.header-decoration {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
    position: relative;
    z-index: 1;
}

.decoration-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: pulse 2s ease-in-out infinite;
}

.decoration-dot:nth-child(2) {
    animation-delay: 0.3s;
}

.decoration-dot:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.form-container {
    padding: 2.5rem;
}

.form-group {
    margin-bottom: 1.75rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-group input[type="text"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.form-group input[type="text"]:focus,
.form-group input[type="tel"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: var(--bg-primary);
    transform: translateY(-1px);
}

.form-group input[type="file"] {
    width: 100%;
    padding: 1.5rem;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    position: relative;
}

.form-group input[type="file"]:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
    transform: translateY(-1px);
}

.form-group input[type="file"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.file-preview {
    margin-top: 1.25rem;
    text-align: center;
}

.file-preview img {
    max-width: 240px;
    max-height: 240px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 3px solid var(--bg-primary);
}

.submit-btn {
    width: 100%;
    padding: 1.25rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

/* 错误提示样式 */
.error {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
}

.form-group.has-error input {
    border-color: #e74c3c;
}

/* 成功提示样式 */
.success {
    color: #27ae60;
    font-size: 14px;
    margin-top: 5px;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 20px;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 0.5rem;
    }

    .container {
        margin: 0;
        border-radius: var(--radius-lg);
    }

    .header {
        padding: 2rem 1.5rem;
    }

    .header h1 {
        font-size: 1.75rem;
    }

    .header p {
        font-size: 1rem;
    }

    .header-icon svg {
        width: 2.5rem;
        height: 2.5rem;
    }

    .form-container {
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        font-size: 0.8rem;
    }

    .form-group input[type="text"],
    .form-group input[type="tel"] {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .submit-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 1.5rem 1rem;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .form-container {
        padding: 1rem;
    }

    .decoration-dot {
        width: 6px;
        height: 6px;
    }
}
