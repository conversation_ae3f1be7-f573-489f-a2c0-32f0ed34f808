<?php
class PhotoAPI {
    private $apiKey;
    private $baseUrl = 'https://api.zjzapi.com/idcardv5/';

    public function __construct($apiKey = null) {
        if ($apiKey) {
            $this->apiKey = $apiKey;
        } else {
            // 从配置文件读取API密钥
            $config = require __DIR__ . '/../config/api.php';
            $this->apiKey = $config['photo_api_key'];
        }
    }
    
    // 生成证件照（有水印）
    public function makeIdPhoto($imageBase64, $itemId = 1, $colors = 'blue') {
        $url = $this->baseUrl . 'make';
        
        $data = [
            'key' => $this->apiKey,
            'item_id' => $itemId,
            'image' => $imageBase64,
            'colors' => $colors,
            'enhance' => 1,
            'beauty' => 1,
            'whitening' => 60
        ];
        
        return $this->sendRequest($url, $data);
    }
    
    // 生成证件照（无水印）
    public function makeIdPhotoNoWatermark($imageBase64, $itemId = 1, $colors = 'blue') {
        $url = $this->baseUrl . 'all';
        
        $data = [
            'key' => $this->apiKey,
            'item_id' => $itemId,
            'image' => $imageBase64,
            'colors' => $colors,
            'enhance' => 1,
            'beauty' => 1,
            'whitening' => 60
        ];
        
        return $this->sendRequest($url, $data);
    }
    
    // 换装
    public function changeClothes($picId, $clothe) {
        $url = $this->baseUrl . 'clothe';
        
        $data = [
            'key' => $this->apiKey,
            'pic_id' => $picId,
            'clothe' => $clothe
        ];
        
        return $this->sendRequest($url, $data);
    }
    
    // 获取无水印照片
    public function getPhoto($picId, $clothe = null) {
        $url = $this->baseUrl . 'get';
        
        $data = [
            'key' => $this->apiKey,
            'pic_id' => $picId
        ];
        
        if ($clothe) {
            $data['clothe'] = $clothe;
        }
        
        return $this->sendRequest($url, $data);
    }
    
    // 发送HTTP请求
    private function sendRequest($url, $data) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['code' => -1, 'msg' => 'CURL错误: ' . $error];
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ['code' => -1, 'msg' => 'HTTP错误: ' . $httpCode];
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['code' => -1, 'msg' => 'JSON解析错误'];
        }
        
        return $result;
    }
    
    // 将图片文件转换为base64
    public static function imageToBase64($imagePath) {
        if (!file_exists($imagePath)) {
            return false;
        }
        
        $imageData = file_get_contents($imagePath);
        return base64_encode($imageData);
    }
    
    // 将上传的文件转换为base64
    public static function uploadedFileToBase64($uploadedFile) {
        if (!isset($uploadedFile['tmp_name']) || !file_exists($uploadedFile['tmp_name'])) {
            return false;
        }
        
        $imageData = file_get_contents($uploadedFile['tmp_name']);
        return base64_encode($imageData);
    }
    
    // 获取可用的服装列表
    public function getClothesOptions() {
        // 这里返回一些常用的服装选项
        return [
            'blue-suit_01' => '蓝色西装1',
            'blue-suit_02' => '蓝色西装2',
            'black-suit_01' => '黑色西装1',
            'black-suit_02' => '黑色西装2',
            'white-shirt_01' => '白色衬衫1',
            'white-shirt_02' => '白色衬衫2',
            'blue-shirt_01' => '蓝色衬衫1',
            'blue-shirt_02' => '蓝色衬衫2'
        ];
    }
}
?>
