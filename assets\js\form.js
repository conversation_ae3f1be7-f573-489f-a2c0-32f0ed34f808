$(document).ready(function() {
    // 文件预览功能
    $('#collection_code').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = $('#codePreview');
                preview.html('<img src="' + e.target.result + '" alt="采集码预览">');
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 表单验证
    function validateForm() {
        let isValid = true;
        
        // 清除之前的错误提示
        $('.form-group').removeClass('has-error');
        $('.error').remove();
        
        // 验证姓名
        const name = $('#name').val().trim();
        if (!name) {
            showError('#name', '请输入姓名');
            isValid = false;
        } else if (name.length < 2) {
            showError('#name', '姓名至少2个字符');
            isValid = false;
        }
        
        // 验证身份证号
        const idcard = $('#idcard').val().trim();
        if (!idcard) {
            showError('#idcard', '请输入身份证号');
            isValid = false;
        } else if (!validateIdCard(idcard)) {
            showError('#idcard', '身份证号格式不正确');
            isValid = false;
        }
        
        // 验证手机号
        const phone = $('#phone').val().trim();
        if (!phone) {
            showError('#phone', '请输入手机号');
            isValid = false;
        } else if (!validatePhone(phone)) {
            showError('#phone', '手机号格式不正确');
            isValid = false;
        }
        
        // 验证学号
        const studentId = $('#student_id').val().trim();
        if (!studentId) {
            showError('#student_id', '请输入学号');
            isValid = false;
        }
        
        // 验证采集码文件
        const collectionCode = $('#collection_code')[0].files[0];
        if (!collectionCode) {
            showError('#collection_code', '请上传采集码图片');
            isValid = false;
        } else if (!validateImageFile(collectionCode)) {
            showError('#collection_code', '请上传有效的图片文件（JPG、PNG、GIF）');
            isValid = false;
        }
        
        return isValid;
    }
    
    // 显示错误信息
    function showError(selector, message) {
        const $field = $(selector);
        $field.closest('.form-group').addClass('has-error');
        $field.after('<div class="error">' + message + '</div>');
    }
    
    // 验证身份证号
    function validateIdCard(idcard) {
        const pattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        return pattern.test(idcard);
    }
    
    // 验证手机号
    function validatePhone(phone) {
        const pattern = /^1[3-9]\d{9}$/;
        return pattern.test(phone);
    }
    
    // 验证图片文件
    function validateImageFile(file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        return allowedTypes.includes(file.type);
    }
    
    // 表单提交
    $('#studentForm').on('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        const submitBtn = $('.submit-btn');
        submitBtn.prop('disabled', true).text('提交中...');
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('name', $('#name').val().trim());
        formData.append('idcard', $('#idcard').val().trim());
        formData.append('phone', $('#phone').val().trim());
        formData.append('student_id', $('#student_id').val().trim());
        formData.append('collection_code', $('#collection_code')[0].files[0]);
        
        // 提交表单数据
        $.ajax({
            url: 'api/submit_form.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // 保存学生ID到session
                    sessionStorage.setItem('student_id', response.student_id);
                    
                    // 跳转到支付页面
                    window.location.href = 'payment.php?student_id=' + response.student_id;
                } else {
                    alert('提交失败：' + response.message);
                    submitBtn.prop('disabled', false).text('提交信息并支付');
                }
            },
            error: function(xhr, status, error) {
                console.error('提交错误:', error);
                alert('提交失败，请重试');
                submitBtn.prop('disabled', false).text('提交信息并支付');
            }
        });
    });
    
    // 实时验证
    $('#name').on('blur', function() {
        const name = $(this).val().trim();
        const $group = $(this).closest('.form-group');
        $group.removeClass('has-error');
        $group.find('.error').remove();
        
        if (name && name.length < 2) {
            showError('#name', '姓名至少2个字符');
        }
    });
    
    $('#idcard').on('blur', function() {
        const idcard = $(this).val().trim();
        const $group = $(this).closest('.form-group');
        $group.removeClass('has-error');
        $group.find('.error').remove();
        
        if (idcard && !validateIdCard(idcard)) {
            showError('#idcard', '身份证号格式不正确');
        }
    });
    
    $('#phone').on('blur', function() {
        const phone = $(this).val().trim();
        const $group = $(this).closest('.form-group');
        $group.removeClass('has-error');
        $group.find('.error').remove();
        
        if (phone && !validatePhone(phone)) {
            showError('#phone', '手机号格式不正确');
        }
    });
});
