<?php
class Database {
    private $connection;
    private $config;
    
    public function __construct() {
        $this->config = require __DIR__ . '/../config/database.php';
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};dbname={$this->config['database']};charset={$this->config['charset']}";
            $this->connection = new PDO($dsn, $this->config['username'], $this->config['password']);
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // 插入学生信息
    public function insertStudent($data) {
        $sql = "INSERT INTO students (name, idcard, phone, student_id, collection_code_url, created_at) 
                VALUES (:name, :idcard, :phone, :student_id, :collection_code_url, NOW())";
        
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute([
            ':name' => $data['name'],
            ':idcard' => $data['idcard'],
            ':phone' => $data['phone'],
            ':student_id' => $data['student_id'],
            ':collection_code_url' => $data['collection_code_url']
        ]);
    }
    
    // 更新学生照片信息
    public function updateStudentPhoto($studentId, $photoUrl, $picId = null) {
        $sql = "UPDATE students SET photo_url = :photo_url, pic_id = :pic_id, updated_at = NOW() 
                WHERE id = :student_id";
        
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute([
            ':photo_url' => $photoUrl,
            ':pic_id' => $picId,
            ':student_id' => $studentId
        ]);
    }
    
    // 插入支付记录
    public function insertPayment($studentId, $amount, $paymentMethod = 'wechat') {
        $sql = "INSERT INTO payments (student_id, amount, payment_method, status, created_at) 
                VALUES (:student_id, :amount, :payment_method, 'pending', NOW())";
        
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute([
            ':student_id' => $studentId,
            ':amount' => $amount,
            ':payment_method' => $paymentMethod
        ]);
    }
    
    // 更新支付状态
    public function updatePaymentStatus($paymentId, $status, $transactionId = null) {
        $sql = "UPDATE payments SET status = :status, transaction_id = :transaction_id, updated_at = NOW() 
                WHERE id = :payment_id";
        
        $stmt = $this->connection->prepare($sql);
        return $stmt->execute([
            ':status' => $status,
            ':transaction_id' => $transactionId,
            ':payment_id' => $paymentId
        ]);
    }
    
    // 根据ID获取学生信息
    public function getStudentById($id) {
        $sql = "SELECT * FROM students WHERE id = :id";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute([':id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // 根据身份证号获取学生信息
    public function getStudentByIdcard($idcard) {
        $sql = "SELECT * FROM students WHERE idcard = :idcard";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute([':idcard' => $idcard]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // 获取支付记录
    public function getPaymentByStudentId($studentId) {
        $sql = "SELECT * FROM payments WHERE student_id = :student_id ORDER BY created_at DESC LIMIT 1";
        $stmt = $this->connection->prepare($sql);
        $stmt->execute([':student_id' => $studentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
