<?php
class QiniuUpload {
    private $accessKey;
    private $secretKey;
    private $bucket;
    private $domain;
    
    public function __construct() {
        $config = require __DIR__ . '/../config/qiniu.php';
        $this->accessKey = $config['accessKey'];
        $this->secretKey = $config['secretKey'];
        $this->bucket = $config['bucket'];
        $this->domain = $config['domain'];
    }
    
    // 生成上传token
    public function getUploadToken($key = null) {
        $policy = [
            'scope' => $key ? $this->bucket . ':' . $key : $this->bucket,
            'deadline' => time() + 3600, // 1小时有效期
            'returnBody' => '{"key":"$(key)","hash":"$(etag)","fsize":$(fsize),"bucket":"$(bucket)","name":"$(x:name)"}'
        ];
        
        $policyStr = json_encode($policy);
        $encodedPolicy = $this->base64UrlEncode($policyStr);
        $sign = hash_hmac('sha1', $encodedPolicy, $this->secretKey, true);
        $encodedSign = $this->base64UrlEncode($sign);
        
        return $this->accessKey . ':' . $encodedSign . ':' . $encodedPolicy;
    }
    
    // 上传文件
    public function uploadFile($filePath, $key = null) {
        if (!file_exists($filePath)) {
            return ['error' => '文件不存在'];
        }
        
        if (!$key) {
            $key = $this->generateKey(pathinfo($filePath, PATHINFO_EXTENSION));
        }
        
        $token = $this->getUploadToken($key);
        $url = 'https://upload.qiniup.com/';
        
        $postData = [
            'key' => $key,
            'token' => $token,
            'file' => new CURLFile($filePath)
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => 'CURL错误: ' . $error];
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ['error' => 'HTTP错误: ' . $httpCode];
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'JSON解析错误'];
        }
        
        if (isset($result['key'])) {
            $result['url'] = $this->domain . '/' . $result['key'];
        }
        
        return $result;
    }
    
    // 上传base64图片
    public function uploadBase64($base64Data, $extension = 'jpg') {
        $key = $this->generateKey($extension);
        $tempFile = tempnam(sys_get_temp_dir(), 'qiniu_upload_');
        
        // 移除base64前缀（如果存在）
        if (strpos($base64Data, ',') !== false) {
            $base64Data = explode(',', $base64Data)[1];
        }
        
        $imageData = base64_decode($base64Data);
        if ($imageData === false) {
            return ['error' => 'Base64解码失败'];
        }
        
        file_put_contents($tempFile, $imageData);
        
        $result = $this->uploadFile($tempFile, $key);
        
        // 清理临时文件
        unlink($tempFile);
        
        return $result;
    }
    
    // 上传网络图片
    public function uploadFromUrl($imageUrl, $extension = 'jpg') {
        $key = $this->generateKey($extension);
        $tempFile = tempnam(sys_get_temp_dir(), 'qiniu_upload_');
        
        $imageData = file_get_contents($imageUrl);
        if ($imageData === false) {
            return ['error' => '无法获取网络图片'];
        }
        
        file_put_contents($tempFile, $imageData);
        
        $result = $this->uploadFile($tempFile, $key);
        
        // 清理临时文件
        unlink($tempFile);
        
        return $result;
    }
    
    // 生成唯一的文件key
    private function generateKey($extension) {
        $date = date('Y/m/d');
        $filename = uniqid() . '_' . time();
        return $date . '/' . $filename . '.' . $extension;
    }
    
    // Base64 URL安全编码
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
}
?>
