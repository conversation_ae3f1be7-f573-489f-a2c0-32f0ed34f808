<?php
session_start();
header('Content-Type: application/json');

try {
    // 清除相关session变量
    unset($_SESSION['form_submitted']);
    unset($_SESSION['payment_success']);
    unset($_SESSION['photo_completed']);
    unset($_SESSION['student_id']);
    unset($_SESSION['payment_id']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Session已清除'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
