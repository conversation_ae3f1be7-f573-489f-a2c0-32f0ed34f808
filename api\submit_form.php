<?php
session_start();
header('Content-Type: application/json');

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/QiniuUpload.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    $requiredFields = ['name', 'idcard', 'phone', 'student_id'];
    foreach ($requiredFields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception('缺少必要字段：' . $field);
        }
    }
    
    // 验证文件上传
    if (!isset($_FILES['collection_code']) || $_FILES['collection_code']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('采集码图片上传失败');
    }
    
    // 获取并验证表单数据
    $name = trim($_POST['name']);
    $idcard = trim($_POST['idcard']);
    $phone = trim($_POST['phone']);
    $studentId = trim($_POST['student_id']);
    $collectionCodeFile = $_FILES['collection_code'];
    
    // 数据验证
    if (strlen($name) < 2) {
        throw new Exception('姓名至少2个字符');
    }
    
    if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idcard)) {
        throw new Exception('身份证号格式不正确');
    }
    
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        throw new Exception('手机号格式不正确');
    }
    
    // 验证文件类型和大小
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($collectionCodeFile['type'], $allowedTypes)) {
        throw new Exception('采集码图片格式不正确，请上传JPG、PNG或GIF格式');
    }
    
    if ($collectionCodeFile['size'] > 8 * 1024 * 1024) { // 8MB
        throw new Exception('采集码图片不能超过8MB');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 检查身份证号是否已存在
    $existingStudent = $db->getStudentByIdcard($idcard);
    if ($existingStudent) {
        throw new Exception('该身份证号已经提交过信息');
    }
    
    // 上传采集码图片到七牛云
    $qiniu = new QiniuUpload();
    $uploadResult = $qiniu->uploadFile($collectionCodeFile['tmp_name']);
    
    if (isset($uploadResult['error'])) {
        throw new Exception('采集码图片上传失败：' . $uploadResult['error']);
    }
    
    $collectionCodeUrl = $uploadResult['url'];
    
    // 准备插入数据库的数据
    $studentData = [
        'name' => $name,
        'idcard' => $idcard,
        'phone' => $phone,
        'student_id' => $studentId,
        'collection_code_url' => $collectionCodeUrl
    ];
    
    // 插入学生信息
    if (!$db->insertStudent($studentData)) {
        throw new Exception('学生信息保存失败');
    }
    
    // 获取插入的学生ID
    $conn = $db->getConnection();
    $newStudentId = $conn->lastInsertId();
    
    // 保存学生ID到session
    $_SESSION['student_id'] = $newStudentId;
    $_SESSION['form_submitted'] = true;
    
    // 返回成功结果
    echo json_encode([
        'success' => true,
        'student_id' => $newStudentId,
        'collection_code_url' => $collectionCodeUrl,
        'message' => '信息提交成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
