<?php
// 拍照生成页面
session_start();

// 调试模式：跳过支付检查
$debug_mode = true;

if (!$debug_mode) {
    // 检查是否已支付
    if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
        header('Location: index.php');
        exit;
    }
} else {
    // 调试模式：设置模拟的session数据
    $_SESSION['payment_success'] = true;
    $_SESSION['student_id'] = 1; // 模拟学生ID
}

// 引入配置文件
$dbConfig = require_once 'config/database.php';
require_once 'includes/Database.php';
require_once 'includes/PhotoAPI.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>证件照制作</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/photo.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <style>
        /* 添加动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .container {
            animation: fadeInUp 0.6s ease-out;
        }

        .camera-container {
            animation: fadeInUp 0.6s ease-out 0.2s both;
        }

        .original-photo {
            animation: slideInLeft 0.6s ease-out 0.4s both;
        }

        .generated-photo {
            animation: slideInRight 0.6s ease-out 0.6s both;
        }

        .clothes-section {
            animation: fadeInUp 0.6s ease-out 0.8s both;
        }

        .action-buttons {
            animation: fadeInUp 0.6s ease-out 1s both;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 2L10.09 8.26L16 9L10.09 9.74L9 16L7.91 9.74L2 9L7.91 8.26L9 2Z" fill="currentColor"/>
                    <circle cx="15.5" cy="6.5" r="1.5" fill="currentColor"/>
                    <rect x="4" y="10" width="16" height="10" rx="2" fill="currentColor" fill-opacity="0.3"/>
                    <circle cx="12" cy="15" r="3" fill="currentColor"/>
                </svg>
            </div>
            <h1>证件照制作</h1>
            <p>请拍照或上传照片，系统将自动生成二寸蓝底证件照</p>
            <div class="progress-bar">
                <div class="progress-step active">
                    <div class="step-number">1</div>
                    <div class="step-label">拍照上传</div>
                </div>
                <div class="progress-line"></div>
                <div class="progress-step">
                    <div class="step-number">2</div>
                    <div class="step-label">生成证件照</div>
                </div>
                <div class="progress-line"></div>
                <div class="progress-step">
                    <div class="step-number">3</div>
                    <div class="step-label">选择服装</div>
                </div>
                <div class="progress-line"></div>
                <div class="progress-step">
                    <div class="step-number">4</div>
                    <div class="step-label">完成保存</div>
                </div>
            </div>
        </div>
        
        <div class="photo-section">
            <!-- 拍照/上传区域 -->
            <div class="camera-container">
                <video id="video" autoplay></video>
                <canvas id="canvas" style="display: none;"></canvas>
                <div class="camera-controls">
                    <button id="startCamera" class="btn btn-primary">开启摄像头</button>
                    <button id="takePhoto" class="btn btn-success" style="display: none;">拍照</button>
                    <input type="file" id="uploadPhoto" accept="image/*" style="display: none;">
                    <button id="uploadBtn" class="btn btn-secondary">上传照片</button>
                </div>
            </div>
            
            <!-- 照片预览区域 -->
            <div class="preview-container">
                <div class="original-photo">
                    <h3>原始照片</h3>
                    <img id="originalImg" src="" alt="原始照片" style="display: none;">
                </div>
                
                <div class="generated-photo">
                    <h3>生成的证件照</h3>
                    <img id="generatedImg" src="" alt="证件照" style="display: none;">
                    <div class="loading" id="loading" style="display: none;">
                        <p>正在生成证件照，请稍候...</p>
                    </div>
                    <div class="loading-pulse" id="loadingPulse" style="display: none;">
                        <div class="pulse-dot"></div>
                        <div class="pulse-dot"></div>
                        <div class="pulse-dot"></div>
                    </div>
                </div>
            </div>
            
            <!-- 换装区域 -->
            <div class="clothes-section" id="clothesSection" style="display: none;">
                <h3>选择服装</h3>
                <div class="clothes-grid" id="clothesGrid">
                    <!-- 服装选项将通过JavaScript动态加载 -->
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons" id="actionButtons" style="display: none;">
                <button id="retakeBtn" class="btn btn-warning">重新拍照</button>
                <button id="changeClothesBtn" class="btn btn-info">更换服装</button>
                <button id="confirmBtn" class="btn btn-success">确认并保存</button>
            </div>
        </div>
    </div>
    
    <script src="assets/js/photo.js"></script>
</body>
</html>
