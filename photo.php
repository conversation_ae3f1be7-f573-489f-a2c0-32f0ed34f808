<?php
// 拍照生成页面
session_start();

// 检查是否已支付
if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
    header('Location: index.php');
    exit;
}

// 引入配置文件
$dbConfig = require_once 'config/database.php';
require_once 'includes/Database.php';
require_once 'includes/PhotoAPI.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>证件照制作</title>
    <link rel="stylesheet" href="assets/css/photo.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>证件照制作</h1>
            <p>请拍照或上传照片，系统将自动生成二寸蓝底证件照</p>
        </div>
        
        <div class="photo-section">
            <!-- 拍照/上传区域 -->
            <div class="camera-container">
                <video id="video" autoplay></video>
                <canvas id="canvas" style="display: none;"></canvas>
                <div class="camera-controls">
                    <button id="startCamera" class="btn btn-primary">开启摄像头</button>
                    <button id="takePhoto" class="btn btn-success" style="display: none;">拍照</button>
                    <input type="file" id="uploadPhoto" accept="image/*" style="display: none;">
                    <button id="uploadBtn" class="btn btn-secondary">上传照片</button>
                </div>
            </div>
            
            <!-- 照片预览区域 -->
            <div class="preview-container">
                <div class="original-photo">
                    <h3>原始照片</h3>
                    <img id="originalImg" src="" alt="原始照片" style="display: none;">
                </div>
                
                <div class="generated-photo">
                    <h3>生成的证件照</h3>
                    <img id="generatedImg" src="" alt="证件照" style="display: none;">
                    <div class="loading" id="loading" style="display: none;">
                        <p>正在生成证件照，请稍候...</p>
                    </div>
                </div>
            </div>
            
            <!-- 换装区域 -->
            <div class="clothes-section" id="clothesSection" style="display: none;">
                <h3>选择服装</h3>
                <div class="clothes-grid" id="clothesGrid">
                    <!-- 服装选项将通过JavaScript动态加载 -->
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons" id="actionButtons" style="display: none;">
                <button id="retakeBtn" class="btn btn-warning">重新拍照</button>
                <button id="changeClothesBtn" class="btn btn-info">更换服装</button>
                <button id="confirmBtn" class="btn btn-success">确认并保存</button>
            </div>
        </div>
    </div>
    
    <script src="assets/js/photo.js"></script>
</body>
</html>
