<?php
// API配置文件
return [
    // 证件照API配置
    'photo_api_key' => 'iI4suMYksGPwig5O72bF7nMpbXXD_vJk', // 需要替换为实际的API密钥
    'photo_api_url' => 'https://api.zjzapi.com/idcardv5/',
    
    // 默认配置
    'default_item_id' => 1, // 二寸照片规格ID
    'default_colors' => 'blue', // 默认蓝底
    'default_enhance' => 1, // 人像增强
    'default_beauty' => 1, // 美颜
    'default_whitening' => 60, // 美白等级
    
    // 文件上传限制
    'max_file_size' => 8 * 1024 * 1024, // 8MB
    'allowed_file_types' => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    
    // 支付配置
    'payment_amount' => 45.00, // 支付金额
    
    // 系统配置
    'site_name' => '毕业信息采集系统',
    'site_description' => '毕业生信息采集和证件照制作系统',
    'admin_email' => '<EMAIL>',
    
    // 错误码定义
    'error_codes' => [
        1001 => '参数错误',
        1002 => '文件上传失败',
        1003 => '数据库操作失败',
        1004 => '支付失败',
        1005 => 'API调用失败',
        1006 => '权限不足',
        1007 => '数据不存在',
        1008 => '操作超时',
        1009 => '系统维护中',
        1010 => '未知错误'
    ]
];
?>
