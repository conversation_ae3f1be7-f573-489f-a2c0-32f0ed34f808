项目背景

开发一个基于 PHP+MySQL 的 H5 页面毕业信息采集系统，需对接第三方 API 实现二寸蓝底照片生成及换装功能。

功能清单

表单采集信息

姓名（text）

身份证号（text，18位校验）

手机号（text，正则校验）

学号（text）

采集码（图片上传/拍照，文件检测合格性，失败提示重新上传）

支付功能

支付金额：45元

支付成功后跳转到照片生成页

照片生成与换装

使用 API（https://www.zjzapi.com/doc/v5.html）生成二寸蓝底证件照

API 返回后，照片在前端实时显示

用户可调用换装 API（例如西装、衬衫、学士服）

提供“重拍”与“确认使用”按钮

数据提交

确认后，将最终照片保存至云存储（返回 URL）

数据库保存用户表单信息 + 照片 URL

后台管理

支持信息导出（CSV/Excel）

查看已提交学生信息与照片

二、流程优化（分阶段开发）
阶段 1（核心功能优先）

打开 拍照生成二寸蓝底照片页面

点击拍摄/上传照片 → 调用 API → 生成蓝底二寸照片

照片展示在前端 → 提供换装功能（调用 API）

用户确认照片 → 照片上传至云存储 → 保存 URL 至数据库

后台可查看已生成的照片数据

👉 目的：先调试 API 和照片流转逻辑，确保稳定。

阶段 2（表单与支付）

打开表单页面 → 填写姓名/身份证号/手机号/学号/采集码（图片文件）

校验表单完整性 → 点击提交

跳转支付页面（45元）

支付成功 → 自动跳转到 阶段 1 的照片页面

用户完成照片选择 → 数据写入数据库

阶段 3（完善与上线）

前端优化：H5 适配、校验提示、照片实时预览

云存储配置：七牛云 / 阿里云 OSS / 腾讯 COS

后台管理：信息审核、数据导出、统计报表

安全与容错：表单校验、防止重复支付、接口错误回退机制



四、测试优先级

✅ 调试 二寸蓝底照片生成 API（含换装）

✅ 调试 图片上传→云存储→数据库 URL 保存

✅ 接入支付（可先用沙箱环境）

✅ 整合表单输入 + 照片 URL 入库

✅ 后台导出数据