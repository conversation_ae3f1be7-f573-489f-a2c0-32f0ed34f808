<?php
// 微信支付回调通知处理
require_once 'includes/Database.php';
require_once 'includes/WxPay.php';

// 获取微信发送的XML数据
$xmlData = file_get_contents('php://input');

// 记录日志
error_log('微信支付回调: ' . $xmlData);

try {
    // 初始化微信支付
    $wxPay = new WxPay();
    
    // 验证回调数据
    if (!$wxPay->verifyNotify($xmlData)) {
        throw new Exception('签名验证失败');
    }
    
    // 解析XML数据
    $data = [];
    $parser = xml_parser_create();
    xml_parse_into_struct($parser, $xmlData, $vals, $index);
    xml_parser_free($parser);
    
    foreach ($vals as $val) {
        if ($val['level'] == 2 && isset($val['value'])) {
            $data[$val['tag']] = $val['value'];
        }
    }
    
    // 验证必要字段
    if (!isset($data['OUT_TRADE_NO']) || !isset($data['RESULT_CODE'])) {
        throw new Exception('缺少必要字段');
    }
    
    $orderNo = $data['OUT_TRADE_NO'];
    $resultCode = $data['RESULT_CODE'];
    $transactionId = $data['TRANSACTION_ID'] ?? '';
    
    // 初始化数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取支付记录
    $stmt = $conn->prepare("SELECT * FROM payments WHERE order_no = ?");
    $stmt->execute([$orderNo]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$payment) {
        throw new Exception('订单不存在');
    }
    
    // 如果已经处理过，直接返回成功
    if ($payment['status'] === 'success') {
        echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
        exit;
    }
    
    // 更新支付状态
    $status = ($resultCode === 'SUCCESS') ? 'success' : 'failed';
    $paidAt = ($status === 'success') ? date('Y-m-d H:i:s') : null;
    
    $stmt = $conn->prepare("
        UPDATE payments 
        SET status = ?, transaction_id = ?, paid_at = ?, updated_at = NOW()
        WHERE order_no = ?
    ");
    $stmt->execute([$status, $transactionId, $paidAt, $orderNo]);
    
    // 如果支付成功，更新学生状态
    if ($status === 'success') {
        $stmt = $conn->prepare("UPDATE students SET status = 'paid', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$payment['student_id']]);
        
        // 记录成功日志
        error_log('支付成功: 订单号=' . $orderNo . ', 学生ID=' . $payment['student_id']);
    }
    
    // 返回成功响应
    echo '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
    
} catch (Exception $e) {
    // 记录错误日志
    error_log('微信支付回调处理失败: ' . $e->getMessage());
    
    // 返回失败响应
    echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[' . $e->getMessage() . ']]></return_msg></xml>';
} catch (Error $e) {
    // 记录错误日志
    error_log('微信支付回调系统错误: ' . $e->getMessage());
    
    // 返回失败响应
    echo '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统错误]]></return_msg></xml>';
}
?>
