<?php
session_start();
header('Content-Type: application/json');

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/WxPay.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    if (!isset($_GET['order_no']) || empty($_GET['order_no'])) {
        throw new Exception('缺少订单号');
    }
    
    $orderNo = $_GET['order_no'];
    
    // 初始化数据库
    $db = new Database();
    
    // 获取支付记录
    $conn = $db->getConnection();
    $stmt = $conn->prepare("
        SELECT p.*, s.name as student_name 
        FROM payments p 
        LEFT JOIN students s ON p.student_id = s.id 
        WHERE p.order_no = ?
    ");
    $stmt->execute([$orderNo]);
    $payment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$payment) {
        throw new Exception('订单不存在');
    }
    
    // 如果已经是成功状态，直接返回
    if ($payment['status'] === 'success') {
        echo json_encode([
            'success' => true,
            'status' => 'success',
            'message' => '支付成功'
        ]);
        exit;
    }
    
    // 如果是失败或取消状态，直接返回
    if (in_array($payment['status'], ['failed', 'cancelled'])) {
        echo json_encode([
            'success' => true,
            'status' => $payment['status'],
            'message' => '支付' . ($payment['status'] === 'failed' ? '失败' : '已取消')
        ]);
        exit;
    }
    
    // 查询第三方支付状态
    $paymentStatus = 'pending';
    
    if ($payment['payment_method'] === 'wechat') {
        $wxPay = new WxPay();
        $result = $wxPay->queryOrder($orderNo);
        
        if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
            $tradeState = $result['trade_state'];
            
            switch ($tradeState) {
                case 'SUCCESS':
                    $paymentStatus = 'success';
                    break;
                case 'PAYERROR':
                case 'REVOKED':
                    $paymentStatus = 'failed';
                    break;
                case 'CLOSED':
                    $paymentStatus = 'cancelled';
                    break;
                default:
                    $paymentStatus = 'pending';
                    break;
            }
            
            // 更新支付状态
            if ($paymentStatus !== 'pending') {
                $stmt = $conn->prepare("
                    UPDATE payments 
                    SET status = ?, transaction_id = ?, paid_at = ?, updated_at = NOW()
                    WHERE order_no = ?
                ");
                
                $paidAt = ($paymentStatus === 'success') ? date('Y-m-d H:i:s') : null;
                $transactionId = $result['transaction_id'] ?? $payment['transaction_id'];
                
                $stmt->execute([$paymentStatus, $transactionId, $paidAt, $orderNo]);
                
                // 如果支付成功，更新学生状态
                if ($paymentStatus === 'success') {
                    $stmt = $conn->prepare("UPDATE students SET status = 'paid', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$payment['student_id']]);
                    
                    // 设置session标记
                    $_SESSION['payment_success'] = true;
                    $_SESSION['student_id'] = $payment['student_id'];
                }
            }
        }
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'status' => $paymentStatus,
        'order_no' => $orderNo,
        'amount' => $payment['amount'],
        'payment_method' => $payment['payment_method'],
        'message' => '查询成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
