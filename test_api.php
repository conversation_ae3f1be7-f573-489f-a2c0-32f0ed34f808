<?php
// API测试页面
require_once 'includes/PhotoAPI.php';

echo "<h1>证件照API测试</h1>";

try {
    // 初始化API
    $photoAPI = new PhotoAPI();
    echo "<p>✅ API初始化成功</p>";
    
    // 获取服装选项
    $clothes = $photoAPI->getClothesOptions();
    echo "<h2>可用服装选项：</h2>";
    echo "<ul>";
    foreach ($clothes as $key => $name) {
        echo "<li>{$key}: {$name}</li>";
    }
    echo "</ul>";
    
    // 显示API配置信息
    $config = require 'config/api.php';
    echo "<h2>API配置信息：</h2>";
    echo "<p>API密钥: " . substr($config['photo_api_key'], 0, 10) . "...</p>";
    echo "<p>API地址: " . $config['photo_api_url'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='debug_photo.php'>进入拍照调试页面</a></p>";
echo "<p><a href='photo.php'>直接访问拍照页面</a></p>";
?>
