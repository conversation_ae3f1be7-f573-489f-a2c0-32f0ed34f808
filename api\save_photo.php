<?php
session_start();
header('Content-Type: application/json');

// 调试模式：跳过支付检查
$debug_mode = true;

if (!$debug_mode) {
    // 检查是否已支付
    if (!isset($_SESSION['payment_success']) || !$_SESSION['payment_success']) {
        echo json_encode(['success' => false, 'message' => '请先完成支付']);
        exit;
    }
}

// 引入必要的类
require_once '../includes/Database.php';
require_once '../includes/PhotoAPI.php';
require_once '../includes/QiniuUpload.php';

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法不正确');
    }
    
    // 验证必要参数
    if (!isset($_POST['pic_id']) || empty($_POST['pic_id'])) {
        throw new Exception('缺少pic_id参数');
    }
    
    $picId = $_POST['pic_id'];
    $clothe = $_POST['clothe'] ?? null;
    
    // 获取学生ID
    $studentId = $_SESSION['student_id'] ?? null;
    if (!$studentId) {
        throw new Exception('学生信息不存在');
    }
    
    // 初始化数据库
    $db = new Database();
    
    // 验证学生是否存在且已支付
    $student = $db->getStudentById($studentId);
    if (!$student) {
        throw new Exception('学生信息不存在');
    }
    
    if ($student['status'] !== 'paid') {
        throw new Exception('请先完成支付');
    }
    
    // 验证pic_id是否属于当前学生
    $conn = $db->getConnection();
    $stmt = $conn->prepare("SELECT id FROM photo_records WHERE student_id = ? AND pic_id = ?");
    $stmt->execute([$studentId, $picId]);
    $photoRecord = $stmt->fetch();
    
    if (!$photoRecord) {
        throw new Exception('无效的pic_id');
    }
    
    // 获取API密钥
    $apiConfig = require_once '../config/api.php';
    $apiKey = $apiConfig['photo_api_key'];
    
    // 初始化照片API
    $photoAPI = new PhotoAPI($apiKey);
    
    // 调用API获取无水印照片（需要付费）
    $result = $photoAPI->getPhoto($picId, $clothe);
    
    if ($result['code'] !== 0) {
        throw new Exception('获取最终照片失败：' . $result['msg']);
    }
    
    $finalPhotos = $result['data']['list'];
    
    // 获取蓝底照片URL
    $bluePhotoUrl = $finalPhotos['blue'] ?? null;
    if (!$bluePhotoUrl) {
        throw new Exception('未找到蓝底照片');
    }
    
    // 将最终照片上传到七牛云
    $qiniu = new QiniuUpload();
    $uploadResult = $qiniu->uploadFromUrl($bluePhotoUrl, 'jpg');
    
    if (isset($uploadResult['error'])) {
        // 如果上传失败，直接使用API返回的URL
        $finalPhotoUrl = $bluePhotoUrl;
    } else {
        $finalPhotoUrl = $uploadResult['url'];
    }
    
    // 更新数据库记录
    $stmt = $conn->prepare("
        UPDATE photo_records 
        SET final_photo_url = ?, api_cost = api_cost + 0.6, updated_at = NOW()
        WHERE student_id = ? AND pic_id = ?
    ");
    $stmt->execute([$finalPhotoUrl, $studentId, $picId]);
    
    // 更新学生表
    $stmt = $conn->prepare("
        UPDATE students 
        SET photo_url = ?, status = 'completed', updated_at = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$finalPhotoUrl, $studentId]);
    
    // 清除session中的支付状态
    unset($_SESSION['payment_success']);
    $_SESSION['photo_completed'] = true;
    
    // 返回成功结果
    echo json_encode([
        'success' => true,
        'photo_url' => $finalPhotoUrl,
        'photos' => $finalPhotos,
        'message' => '照片保存成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?>
